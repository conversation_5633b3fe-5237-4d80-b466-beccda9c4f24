#!/usr/bin/env python3
"""
Optuna超参数优化脚本
目标：找到最佳超参数组合，冲击AUC 0.84
"""

import optuna
import torch
import numpy as np
from train import train_model
import argparse
import json
import os
from datetime import datetime

def objective(trial):
    """Optuna优化目标函数"""
    
    # 建议的超参数搜索空间
    params = {
        # 学习率 - 最重要的参数
        'lr': trial.suggest_float('lr', 1e-5, 5e-4, log=True),
        
        # DropPath率 - 控制正则化强度
        'drop_path_max': trial.suggest_float('drop_path_max', 0.1, 0.4),
        
        # Dropout率
        'dropout': trial.suggest_float('dropout', 0.2, 0.4),
        
        # 权重衰减
        'weight_decay': trial.suggest_float('weight_decay', 1e-5, 1e-3, log=True),
        
        # SAM参数
        'sam_rho': trial.suggest_float('sam_rho', 0.01, 0.1),
        
        # 学习率调度器参数
        'lr_factor': trial.suggest_float('lr_factor', 0.3, 0.7),
        'lr_patience': trial.suggest_int('lr_patience', 3, 8),
        
        # 模型架构参数
        'hidden_dim': trial.suggest_categorical('hidden_dim', [128, 160, 192]),
        'num_encoder_layers': trial.suggest_int('num_encoder_layers', 3, 6),
    }
    
    # 创建临时配置
    config = {
        'node_features': 1058,
        'edge_features': 16,
        'hidden_dim': params['hidden_dim'],
        'num_encoder_layers': params['num_encoder_layers'],
        'k_neighbors': 30,
        'augment_eps': 0.1,
        'dropout': params['dropout'],
        'id_name': 'ID',
        'obj_max': 1,
        'epochs': 30,  # 减少epoch数以加快搜索
        'patience': 6,
        'batch_size': 32,
        'num_samples': 1675,
        'folds': 2,
        'seed': 2024,
        'focal_alpha': 0.25,
        'focal_gamma': 2.0,
        'use_sam': True,
        'sam_rho': params['sam_rho'],
        'lr': params['lr'],
        'weight_decay': params['weight_decay'],
        'drop_path_max': params['drop_path_max'],
        'lr_factor': params['lr_factor'],
        'lr_patience': params['lr_patience'],
        'pe_type': 'rw',
        'loss_type': 'focal',
        'focal_weight': 0.4,
        'tversky_weight': 0.4,
        'lovasz_weight': 0.2,
        'tversky_alpha': 0.3,
        'tversky_beta': 0.7,
        'remark': f'Optuna trial {trial.number}'
    }
    
    try:
        # 运行训练
        results = train_model_with_config(config)
        
        # 返回平均AUC作为优化目标
        avg_auc = np.mean([results[f'fold_{i}_auc'] for i in range(config['folds'])])
        
        # 记录试验结果
        trial.set_user_attr('avg_auc', avg_auc)
        trial.set_user_attr('fold_results', results)
        
        return avg_auc
        
    except Exception as e:
        print(f"Trial {trial.number} failed: {e}")
        return 0.0  # 返回最低分数

def train_model_with_config(config):
    """使用给定配置训练模型"""
    import subprocess
    import tempfile
    import json

    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(config, f)
        config_file = f.name

    try:
        # 运行训练脚本
        cmd = [
            'python', 'train.py',
            '--use_sam',
            '--sam_rho', str(config['sam_rho']),
            '--loss_type', config['loss_type'],
            '--pe_type', config['pe_type'],
            '--n_folds', str(config['folds']),
            '--config_file', config_file
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)

        if result.returncode != 0:
            print(f"Training failed: {result.stderr}")
            return {'fold_0_auc': 0.0, 'fold_1_auc': 0.0}

        # 解析输出获取AUC结果
        output = result.stdout
        fold_aucs = []

        for line in output.split('\n'):
            if 'Fold' in line and 'AUC =' in line:
                auc = float(line.split('AUC = ')[1])
                fold_aucs.append(auc)

        if len(fold_aucs) >= 2:
            return {
                'fold_0_auc': fold_aucs[0],
                'fold_1_auc': fold_aucs[1],
            }
        else:
            return {'fold_0_auc': 0.0, 'fold_1_auc': 0.0}

    except Exception as e:
        print(f"Error running training: {e}")
        return {'fold_0_auc': 0.0, 'fold_1_auc': 0.0}

    finally:
        # 清理临时文件
        try:
            os.unlink(config_file)
        except:
            pass

def main():
    parser = argparse.ArgumentParser(description='Optuna超参数优化')
    parser.add_argument('--n_trials', type=int, default=50, help='优化试验次数')
    parser.add_argument('--study_name', type=str, default='mvgnn_optimization', help='研究名称')
    parser.add_argument('--storage', type=str, default=None, help='数据库存储路径')
    args = parser.parse_args()
    
    # 创建或加载研究
    if args.storage:
        study = optuna.create_study(
            direction='maximize',
            study_name=args.study_name,
            storage=args.storage,
            load_if_exists=True
        )
    else:
        study = optuna.create_study(direction='maximize')
    
    # 开始优化
    print(f"开始Optuna优化，目标试验次数: {args.n_trials}")
    print(f"当前时间: {datetime.now()}")
    
    study.optimize(objective, n_trials=args.n_trials)
    
    # 输出最佳结果
    print("\n" + "="*50)
    print("优化完成！")
    print("="*50)
    
    best_trial = study.best_trial
    print(f"最佳AUC: {best_trial.value:.6f}")
    print(f"最佳参数:")
    for key, value in best_trial.params.items():
        print(f"  {key}: {value}")
    
    # 保存结果
    results = {
        'best_auc': best_trial.value,
        'best_params': best_trial.params,
        'n_trials': len(study.trials),
        'timestamp': datetime.now().isoformat()
    }
    
    with open(f'optuna_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n结果已保存到: optuna_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

if __name__ == '__main__':
    main()
