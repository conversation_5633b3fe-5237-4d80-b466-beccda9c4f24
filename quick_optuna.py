#!/usr/bin/env python3
"""
快速Optuna超参数优化脚本
目标：找到最佳超参数组合，冲击AUC 0.84
"""

import optuna
import subprocess
import json
import tempfile
import os
import re
from datetime import datetime

def objective(trial):
    """Optuna优化目标函数"""
    
    # 重点优化的超参数
    lr = trial.suggest_float('lr', 5e-5, 2e-4, log=True)
    sam_rho = trial.suggest_float('sam_rho', 0.02, 0.08)
    drop_path_max = trial.suggest_float('drop_path_max', 0.15, 0.35)
    dropout = trial.suggest_float('dropout', 0.25, 0.4)
    weight_decay = trial.suggest_float('weight_decay', 5e-5, 2e-4, log=True)
    lr_factor = trial.suggest_float('lr_factor', 0.3, 0.6)
    lr_patience = trial.suggest_int('lr_patience', 3, 7)
    
    # 创建配置
    config = {
        'lr': lr,
        'sam_rho': sam_rho,
        'drop_path_max': drop_path_max,
        'dropout': dropout,
        'weight_decay': weight_decay,
        'lr_factor': lr_factor,
        'lr_patience': lr_patience,
        'epochs': 25,  # 减少epoch以加快搜索
        'patience': 6,
    }
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(config, f)
        config_file = f.name
    
    try:
        # 运行训练
        cmd = [
            'python', 'train.py',
            '--use_sam',
            '--loss_type', 'focal',
            '--pe_type', 'rw',
            '--n_folds', '2',
            '--config_file', config_file
        ]
        
        print(f"Trial {trial.number}: Running with lr={lr:.2e}, sam_rho={sam_rho:.3f}, drop_path={drop_path_max:.2f}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30分钟超时
        
        if result.returncode != 0:
            print(f"Trial {trial.number} failed: {result.stderr}")
            return 0.0
        
        # 解析输出获取平均AUC
        output = result.stdout
        
        # 查找最终的平均AUC
        avg_auc_match = re.search(r'Average AUC: ([\d.]+)', output)
        if avg_auc_match:
            avg_auc = float(avg_auc_match.group(1))
            print(f"Trial {trial.number} completed: AUC = {avg_auc:.6f}")
            return avg_auc
        else:
            print(f"Trial {trial.number}: Could not parse AUC from output")
            return 0.0
            
    except subprocess.TimeoutExpired:
        print(f"Trial {trial.number} timed out")
        return 0.0
    except Exception as e:
        print(f"Trial {trial.number} error: {e}")
        return 0.0
    finally:
        # 清理临时文件
        try:
            os.unlink(config_file)
        except:
            pass

def main():
    print("🚀 开始Optuna超参数优化")
    print(f"目标：从当前的0.778提升到0.84")
    print(f"开始时间: {datetime.now()}")
    print("="*60)
    
    # 创建研究
    study = optuna.create_study(direction='maximize')
    
    # 设置初始参数（基于当前最佳结果）
    study.enqueue_trial({
        'lr': 1e-4,
        'sam_rho': 0.05,
        'drop_path_max': 0.2,
        'dropout': 0.3,
        'weight_decay': 1e-4,
        'lr_factor': 0.5,
        'lr_patience': 5,
    })
    
    # 开始优化
    try:
        study.optimize(objective, n_trials=20, timeout=7200)  # 2小时超时
    except KeyboardInterrupt:
        print("\n优化被用户中断")
    
    # 输出结果
    print("\n" + "="*60)
    print("🎉 优化完成！")
    print("="*60)
    
    if study.best_trial:
        best_trial = study.best_trial
        print(f"🏆 最佳AUC: {best_trial.value:.6f}")
        print(f"📈 提升幅度: {best_trial.value - 0.778:.6f}")
        print(f"🎯 距离目标0.84还差: {0.84 - best_trial.value:.6f}")
        print("\n🔧 最佳参数:")
        for key, value in best_trial.params.items():
            print(f"  {key}: {value}")
        
        # 保存结果
        results = {
            'best_auc': best_trial.value,
            'best_params': best_trial.params,
            'improvement': best_trial.value - 0.778,
            'trials_completed': len(study.trials),
            'timestamp': datetime.now().isoformat()
        }
        
        filename = f'optuna_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 结果已保存到: {filename}")
        
        # 如果有显著提升，给出建议
        if best_trial.value > 0.785:
            print(f"\n🎊 恭喜！AUC提升到{best_trial.value:.6f}，非常接近目标了！")
            print("建议：用这些参数进行完整的5折交叉验证")
        elif best_trial.value > 0.780:
            print(f"\n👍 不错！AUC提升到{best_trial.value:.6f}")
            print("建议：可以继续优化或尝试其他策略")
        else:
            print(f"\n🤔 AUC为{best_trial.value:.6f}，提升有限")
            print("建议：考虑其他优化策略，如数据增强或模型架构调整")
    else:
        print("❌ 没有成功的试验")

if __name__ == '__main__':
    main()
