import torch
import torch.nn as nn
import torch.nn.functional as F
from self_attention import Normalize

class NoGNNBaseline(nn.Module):
    """
    非图基线模型：使用1D-CNN + Bi-LSTM，不使用任何图结构信息
    目标：建立最简单的序列模型基线，验证图结构的必要性
    """
    def __init__(self, node_features, hidden_dim=128, num_layers=2, dropout=0.2):
        super(NoGNNBaseline, self).__init__()
        
        # 输入特征处理
        self.input_norm = nn.LayerNorm(node_features)
        self.input_proj = nn.Linear(node_features, hidden_dim)
        
        # 1D CNN层 - 捕获局部序列模式
        self.conv1d_layers = nn.ModuleList([
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1),
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=5, padding=2),
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=7, padding=3),
        ])
        
        # Bi-LSTM层 - 捕获长程序列依赖
        self.lstm = nn.LSTM(
            input_size=hidden_dim * 3,  # 3个CNN的输出拼接
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            bidirectional=True,
            dropout=dropout if num_layers > 1 else 0
        )
        
        # 输出层
        self.output_norm = nn.LayerNorm(hidden_dim * 2)  # bidirectional
        self.output_proj = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, 1)
        )
        
        self.dropout = nn.Dropout(dropout)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.LSTM):
                for name, param in m.named_parameters():
                    if 'weight' in name:
                        nn.init.xavier_uniform_(param)
                    elif 'bias' in name:
                        nn.init.zeros_(param)
    
    def forward(self, X, V, mask, adj=None, pe=None):
        """
        前向传播 - 只使用序列特征V，忽略坐标X、邻接矩阵adj和位置编码pe
        Args:
            X: 坐标特征 [B, L, 3] - 忽略
            V: 节点特征 [B, L, node_features] - 主要输入
            mask: 序列掩码 [B, L]
            adj: 邻接矩阵 [B, L, L] - 忽略
            pe: 位置编码 [B, L, pe_dim] - 忽略
        Returns:
            logits: 预测结果 [B, L]
        """
        batch_size, seq_len, _ = V.shape
        
        # 1. 输入特征处理
        h = self.input_norm(V)
        h = self.input_proj(h)  # [B, L, hidden_dim]
        h = self.dropout(h)
        
        # 2. 1D CNN处理 - 捕获不同尺度的局部模式
        h_transpose = h.transpose(1, 2)  # [B, hidden_dim, L] for Conv1d
        
        conv_outputs = []
        for conv_layer in self.conv1d_layers:
            conv_out = F.relu(conv_layer(h_transpose))  # [B, hidden_dim, L]
            conv_outputs.append(conv_out)
        
        # 拼接不同卷积核的输出
        h_conv = torch.cat(conv_outputs, dim=1)  # [B, hidden_dim*3, L]
        h_conv = h_conv.transpose(1, 2)  # [B, L, hidden_dim*3]
        h_conv = self.dropout(h_conv)
        
        # 3. Bi-LSTM处理 - 捕获长程依赖
        # 使用pack_padded_sequence处理变长序列
        lengths = mask.sum(dim=1).cpu()  # [B]
        h_packed = nn.utils.rnn.pack_padded_sequence(
            h_conv, lengths, batch_first=True, enforce_sorted=False
        )
        
        lstm_out_packed, _ = self.lstm(h_packed)
        lstm_out, _ = nn.utils.rnn.pad_packed_sequence(
            lstm_out_packed, batch_first=True, total_length=seq_len
        )  # [B, L, hidden_dim*2]
        
        # 4. 输出层
        h_out = self.output_norm(lstm_out)
        h_out = self.dropout(h_out)
        logits = self.output_proj(h_out).squeeze(-1)  # [B, L]
        
        # 5. 应用掩码
        if mask is not None:
            logits = logits * mask
        
        return logits


class SimpleCNNBaseline(nn.Module):
    """
    更简单的CNN基线模型
    """
    def __init__(self, node_features, hidden_dim=128, dropout=0.2):
        super(SimpleCNNBaseline, self).__init__()
        
        self.input_proj = nn.Linear(node_features, hidden_dim)
        
        # 简单的CNN层
        self.conv_layers = nn.Sequential(
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Dropout(dropout),
        )
        
        # 输出层
        self.output_layers = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, 1)
        )
        
    def forward(self, X, V, mask, adj=None, pe=None):
        # 输入处理
        h = self.input_proj(V)  # [B, L, hidden_dim]
        
        # CNN处理
        h = h.transpose(1, 2)  # [B, hidden_dim, L]
        h = self.conv_layers(h)
        h = h.transpose(1, 2)  # [B, L, hidden_dim]
        
        # 输出
        logits = self.output_layers(h).squeeze(-1)  # [B, L]
        
        if mask is not None:
            logits = logits * mask
            
        return logits
