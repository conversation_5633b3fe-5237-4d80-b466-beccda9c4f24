import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.layers import DropPath  # 引入DropPath
from gat_layer_improved import MultiHeadGATLayer  # 添加GAT导入
from gps_enhanced_layer import EnhancedGatedFusion  # 添加GPS门控融合
# The following gather functions
def gather_edges(edges, neighbor_idx):
    # Features [B,N,N,C] at Neighbor indices [B,N,K] => Neighbor features [B,N,K,C]
    neighbors = neighbor_idx.unsqueeze(-1).expand(-1, -1, -1, edges.size(-1))
    edge_features = torch.gather(edges, 2, neighbors)
    return edge_features

def gather_nodes(nodes, neighbor_idx):
    # Features [B,N,C] at Neighbor indices [B,N,K] => [B,N,K,C]
    # Flatten and expand indices per batch [B,N,K] => [B,NK] => [B,NK,C]
    neighbors_flat = neighbor_idx.view((neighbor_idx.shape[0], -1))
    neighbors_flat = neighbors_flat.unsqueeze(-1).expand(-1, -1, nodes.size(2))
    # Gather and re-pack
    neighbor_features = torch.gather(nodes, 1, neighbors_flat)
    neighbor_features = neighbor_features.view(list(neighbor_idx.shape)[:3] + [-1])
    return neighbor_features

def cat_neighbors_nodes(h_nodes, h_neighbors, E_idx):
    h_nodes = gather_nodes(h_nodes, E_idx)
    h_nn = torch.cat([h_neighbors, h_nodes], -1)
    return h_nn


class Normalize(nn.Module): 
    def __init__(self, features, epsilon=1e-6):
        super(Normalize, self).__init__()
        self.gain = nn.Parameter(torch.ones(features))
        self.bias = nn.Parameter(torch.zeros(features))
        self.epsilon = epsilon

    def forward(self, x, dim=-1):
        mu = x.mean(dim, keepdim=True)
        sigma = torch.sqrt(x.var(dim, keepdim=True) + self.epsilon)
        gain = self.gain
        bias = self.bias
        # Reshape
        if dim != -1:
            shape = [1] * len(mu.size())
            shape[dim] = self.gain.size()[0]
            gain = gain.view(shape)
            bias = bias.view(shape)
        return gain * (x - mu) / (sigma + self.epsilon) + bias


class TransformerLayer(nn.Module):
    """
    一个融合了您原有设计、门控更新和DropPath的全新Transformer层。
    集成了现代Transformer的最佳实践，同时保留您的核心创新。
    """
    def __init__(self, num_hidden, num_in, num_heads=4, dropout=0.2, drop_path_prob=0.1):
        super(TransformerLayer, self).__init__()
        self.dropout = nn.Dropout(dropout)
        self.act = nn.GELU()  # 门控机制和现代Transformer中常用GELU

        # --- 1. 保留您原有的核心模块 ---
        self.attention = NeighborAttention(num_hidden, num_in, num_heads)
        self.gat1 = MultiHeadGATLayer(num_hidden, num_hidden, n_heads=4, dropout=dropout)
        # 您的gated_fusion模块设计得很好，我们将继续利用它来产生高质量的更新信号
        self.gated_fusion = EnhancedGatedFusion(num_hidden, dropout=dropout)

        # --- 2. 添加新的现代化模块 ---
        self.norm1 = Normalize(num_hidden)  # 第一个Add&Norm
        self.norm2 = Normalize(num_hidden)  # 第二个Add&Norm (FFN前)
        self.drop_path = DropPath(drop_path_prob) if drop_path_prob > 0. else nn.Identity()
        self.ffn = PositionWiseFeedForward(num_hidden, num_hidden * 4)  # 复用您的FFN

    def forward(self, h_V, h_E, adj, h0, l, mask_V=None, mask_attend=None):
        """
        现代化的前向传播，融合您的创新设计和标准Transformer架构
        """
        # 保存输入，用于第一个残差连接
        shortcut = h_V

        # --- 核心注意力与融合模块 (这部分逻辑来自您的代码) ---
        # 计算局部几何注意力
        dh_attn = self.attention(h_V, h_E, mask_attend)

        # 先进行一次初步的Add&Norm，为GAT和融合模块提供更稳定的输入
        h_V_after_attn = self.norm1(h_V + self.drop_path(self.dropout(dh_attn)))
        h_V_after_attn = self.act(h_V_after_attn)

        # 计算全局拓扑注意力
        dh_gat = self.gat1(h_V_after_attn, adj, mask_V)

        # 使用您设计的融合模块得到最终的"更新信号"
        # 注意：这里的输入可能需要根据您EnhancedGatedFusion的定义微调
        update_signal = self.gated_fusion(h_V_after_attn, dh_attn, dh_gat)

        # --- 应用第一个残差连接 ---
        # 将高质量的融合信号作为更新量，应用DropPath后加到原始输入上
        h_V = shortcut + self.drop_path(self.dropout(update_signal))

        # --- FFN模块 (标准的第二个子层) ---
        # 保存输入，用于第二个残差连接
        ffn_shortcut = h_V
        # 计算FFN更新量
        ffn_update = self.ffn(self.norm2(h_V))
        # 应用第二个残差连接
        h_V = ffn_shortcut + self.drop_path(self.dropout(ffn_update))

        if mask_V is not None:
            mask_V = mask_V.unsqueeze(-1)
            h_V = mask_V * h_V
        return h_V


class PositionWiseFeedForward(nn.Module):
    def __init__(self, num_hidden, num_ff):
        super(PositionWiseFeedForward, self).__init__()
        self.W_in = nn.Linear(num_hidden, num_ff, bias=True)
        self.W_out = nn.Linear(num_ff, num_hidden, bias=True)

    def forward(self, h_V):
        h = F.relu(self.W_in(h_V))
        h = self.W_out(h)
        return h


class NeighborAttention(nn.Module):
    def __init__(self, num_hidden, num_in, num_heads=4):
        super(NeighborAttention, self).__init__()
        self.num_heads = num_heads
        self.num_hidden = num_hidden

        # Self-attention layers: {queries, keys, values, output}
        self.W_Q = nn.Linear(num_hidden, num_hidden, bias=False)
        self.W_K = nn.Linear(num_in, num_hidden, bias=False)
        self.W_V = nn.Linear(num_in, num_hidden, bias=False)
        self.W_O = nn.Linear(num_hidden, num_hidden, bias=False)

    def _masked_softmax(self, attend_logits, mask_attend, dim=-1):
        """ Numerically stable masked softmax """
        negative_inf = np.finfo(np.float32).min
        # 修复设备问题：使用与输入相同的设备
        attend_logits = torch.where(mask_attend > 0, attend_logits,
                                  torch.tensor(negative_inf, device=attend_logits.device, dtype=attend_logits.dtype))
        attend = F.softmax(attend_logits, dim)
        attend = mask_attend * attend
        return attend

    def forward(self, h_V, h_E, mask_attend=None):
        """ Self-attention, graph-structured O(Nk)
        Args:
            h_V:            Node features           [N_batch, N_nodes, N_hidden]
            h_E:            Neighbor features       [N_batch, N_nodes, K, N_hidden] 
            mask_attend:    Mask for attention      [N_batch, N_nodes, K]           mask_attend [B, L, K] 
        Returns:
            h_V:            Node update
        """

        # Queries, Keys, Values
        n_batch, n_nodes, n_neighbors = h_E.shape[:3]
        n_heads = self.num_heads

        d = int(self.num_hidden / n_heads)
        Q = self.W_Q(h_V).view([n_batch, n_nodes, 1, n_heads, 1, d])
        K = self.W_K(h_E).view([n_batch, n_nodes, n_neighbors, n_heads, d, 1])
        V = self.W_V(h_E).view([n_batch, n_nodes, n_neighbors, n_heads, d])

        # Attention with scaled inner product
        attend_logits = torch.matmul(Q, K).view([n_batch, n_nodes, n_neighbors, n_heads]).transpose(-2,-1)
        attend_logits = attend_logits / np.sqrt(d)
        
        if mask_attend is not None:
            # Masked softmax
            mask = mask_attend.unsqueeze(2).expand(-1,-1,n_heads,-1)
            attend = self._masked_softmax(attend_logits, mask) # [B, L, heads, K]
        else:
            attend = F.softmax(attend_logits, -1)

        # Attentive reduction
        h_V_update = torch.matmul(attend.unsqueeze(-2), V.transpose(2,3)) # [B, L, heads, 1, K] × [B, L, heads, K, d]
        h_V_update = h_V_update.view([n_batch, n_nodes, self.num_hidden])
        h_V_update = self.W_O(h_V_update)
        return h_V_update